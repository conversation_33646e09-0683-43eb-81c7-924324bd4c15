import { Box, Divider, IconButton, makeStyles, Typography } from '@material-ui/core';
import { FlashOn, Settings } from '@material-ui/icons';
import React, { useContext, useEffect, useState } from 'react'
import { MultiCropContext } from '../../../../context/MultiCropContext/MultiCropContext';
import { UserContext } from '../../../../context/UserProvider';
import { db } from '../../../../config/firebase';
import { ConfigIndividualPowerConsum } from './ConfigIndividualPowerConsum';

// ESTILOS
const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
    alignItems: 'center',
    background: '#FFFFFF',
    borderRadius: 12,
    padding: theme.spacing(1.5, 2),
    marginBottom: theme.spacing(2),
    boxShadow: '0 1px 6px rgba(16,32,56,.10)',
    minHeight: 56,
    [theme.breakpoints.down('xs')]: {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
  },
  value: {
    margin: theme.spacing(0, 2),
    fontWeight: 500,
    fontSize: '1rem',
    color: '#353a47',
  },
  divider: {
    height: 28,
    margin: theme.spacing(0, 1),
    background: '#e0e0e0',
  },
  label: {
    fontWeight: 600,
    color: '#5BB033',
    marginRight: theme.spacing(2),
  },
  settings: {
    marginLeft: 'auto',
    // color: '#3B3C43',
  },
}));

export const PowerConsumptionBar = ({
    onSettingsClick,
}) => {
	const classes = useStyles();
	const { usuario, currentMac, canIdIrrigation } = useContext(UserContext);
	const { tabValue, allTilesTest } = useContext(MultiCropContext);
	const [togglesIds, setTogglesIds] = useState([])
	const [togglesNames, setTogglesNames] = useState([])
	const [allCurrents, setAllCurrents] = useState([])
	const [amp, setAmp] = useState(0)
	const [volt, setVolt] = useState(127)
	const [watt, setWatt] = useState(0)
	const [openDialogSettings, setOpenDialogSettings] = useState(false)

	useEffect(() => {
	  if (allTilesTest[tabValue] === undefined) return;
	  
	  const togglesTiles = []
	  allTilesTest[tabValue].forEach((array) => {
		if(array.length > 0) {
			const tilesFounded = array.filter((item) => item.kind === '5')
			togglesTiles.push(...tilesFounded)
		}
	  })

	  if(togglesTiles.length > 0) {
		const outIds = togglesTiles.map((item) => {
			const uid = item.uid;
			return uid.split("@")[3]
		})
		const togglesNames = togglesTiles.map((item) => item.name)
		setTogglesIds(outIds)
		setTogglesNames(togglesNames)
	  } else {
		setTogglesIds([])
		setTogglesNames([])
	  }
	  
	}, [allTilesTest, tabValue])

	useEffect(() => {
		if (!usuario.username || currentMac === ""  || canIdIrrigation === "") return;
		const path = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/fromModule`;
		const docRef = db.collection(path).doc("togglesInfo");
		const unsubscribe = docRef.onSnapshot((docSnapshot) => {
			if (docSnapshot.exists) {
				const data = docSnapshot.data();
				const arrayOfCurrents = data.currents;
				setAllCurrents(arrayOfCurrents);
			} else {
				console.log("No se encontró el documento de corrientes");
				setAllCurrents([]);
			}
		})

		return () => {
			unsubscribe();
		};
	}, [canIdIrrigation,currentMac,usuario.username])

	useEffect(() => {
	  if(allCurrents.length > 0 && togglesIds.length > 0) {
		let totalCurrent = 0;
		togglesIds.forEach((outId) => {
			const currentValue = allCurrents[Number(outId)]
			const current = parseFloat(currentValue);
			if (!isNaN(current)) {
				totalCurrent += current;
			} else {
				console.log("Valor de corriente no numérico:", currentValue);
			}
		})
		setAmp(totalCurrent.toFixed(1))
		const powerValue = totalCurrent * volt;
		setWatt(powerValue.toFixed(1))
		
	  } else {
		setAmp(0)
		setWatt(0)
	  }
	}, [allCurrents, togglesIds, volt,tabValue])
	

  return (
	<>

	<Box className={classes.root}>
      <Typography className={classes.label}>{<FlashOn/>} Consumo Eléctrico</Typography>

      <Typography className={classes.value}>Corriente: {amp} A</Typography>
      <Divider orientation="vertical" flexItem className={classes.divider} />

      <Typography className={classes.value}>Tension: {volt} V</Typography>
      <Divider orientation="vertical" flexItem className={classes.divider} />

      <Typography className={classes.value}>Potencia: {watt} W</Typography>

      <IconButton
        className={classes.settings}
        aria-label="Configuración"
        onClick={() => setOpenDialogSettings(true)}
      >
        <Settings />
      </IconButton>
    </Box>
	
	<ConfigIndividualPowerConsum 
	open={openDialogSettings} 
	togglesIds={togglesIds}
	togglesNames={togglesNames}
	onClose={() => setOpenDialogSettings(false)} 
	/>
	</>
  )
}
