import { Grid, Typography, Divider, IconButton, Collapse, Box } from '@material-ui/core'
import { ExpandMore, ExpandLess } from '@material-ui/icons'
import React, { useContext, useEffect, useState } from 'react'
import HeadTile from './HeadTile'
import { UserContext } from '../../../../context/UserProvider';
import { db } from '../../../../config/firebase';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles({
  divider: {
    margin: '8px 0',
    backgroundColor: '#e0e0e0',
  },
  expandButton: {
    padding: '4px',
    marginTop: '8px',
  },
  detailsContainer: {
    maxHeight: '200px',
    overflowY: 'auto',
    padding: '8px',
    border: '1px solid #e0e0e0',
    borderRadius: '4px',
    margin: '8px 0',
  },
  currentItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '4px 8px',
    borderBottom: '1px solid #f0f0f0',
    '&:last-child': {
      borderBottom: 'none',
    },
  },
  currentName: {
    fontSize: '0.8rem',
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginRight: '8px',
    wordBreak: 'break-word',
    lineHeight: '1.2',
  },
  currentValue: {
    fontSize: '0.8rem',
    fontWeight: 'bold',
    color: ' #5BB033',
    minWidth: '60px',
    textAlign: 'right',
  },
});

export const ElectricCurrentSensorTile = ({ data, style, col }) => {
  const classes = useStyles();
  const uid = data.uid;
  const name = data.name;
  const { usuario, togglesNames } = useContext(UserContext);
  const [allCurrents, setAllCurrents] = useState([])
  const [totalCurrent, setTotalCurrent] = useState(0)
  const [totalVoltage, setTotalVoltage] = useState(0)
  const [totalPower, setTotalPower] = useState(0)
  const [showDetails, setShowDetails] = useState(false)

  function sumStringArray(arr) {
    if (!Array.isArray(arr)) return 0;
    return arr.reduce((total, str) => {
      const num = parseFloat(str);
      // Sólo sumamos si num es un número finito
      return !isNaN(num) ? total + num : total;
    }, 0);
  }

  // Función para formatear valores numéricos
  const formatValue = (number, decimals = 2) => {
    if (isNaN(number) || number === null || number === undefined) return "0";
    return Number(number).toFixed(decimals);
  };

  // Función para procesar los datos de corrientes y nombres
  const processCurrentsData = () => {
    if (!togglesNames || !Array.isArray(togglesNames) || togglesNames.length === 0) {
      return [];
    }

    const processedData = [];

    for (let i = 0; i < togglesNames.length; i++) {
      const name = togglesNames[i];
      let currentValue = 0;

      // Si hay valores de corriente disponibles
      if (allCurrents && Array.isArray(allCurrents) && i < allCurrents.length) {
        const current = parseFloat(allCurrents[i]);
        currentValue = !isNaN(current) ? current.toFixed(1) : 0;
      }

      processedData.push({
        name: name,
        current: currentValue
      });
    }

    return processedData;
  };

  // Listener en tiempo real para datos de corriente
  useEffect(() => {
    if (!usuario.username) return;

    const mac = uid.split("@")[0];
    const canId = uid.split("@")[1];
    const path = `${usuario.username}/infoDevices/${mac}/${canId}/fromModule`;
    const docRef = db.collection(path).doc("togglesInfo");

    const unsubscribe = docRef.onSnapshot((docSnapshot) => {
      if (docSnapshot.exists) {
        const data = docSnapshot.data();
        console.log("Datos de corriente actualizados:", data);

        if (data.currents) {
          const arrayOfCurrents = data.currents;
          setAllCurrents(arrayOfCurrents);
          const totalCurrentValue = sumStringArray(arrayOfCurrents);
          const totalCurrentFormatted = totalCurrentValue.toFixed(1);
          setTotalCurrent(totalCurrentFormatted);
          console.log("Corriente total actualizada:", totalCurrentValue);
        }
      } else {
        console.log("No se encontró el documento de corrientes");
        setTotalCurrent(0);
        setAllCurrents([]);
      }
    }, (error) => {
      console.error("Error al escuchar cambios en corrientes:", error);
    });

    return () => {
      unsubscribe();
    };
  }, [usuario.username, uid]);

  // Listener en tiempo real para datos de voltaje
  useEffect(() => {
    if (!usuario.username) return;

    const mac = uid.split("@")[0];
    const canId = uid.split("@")[1];
    const path = `${usuario.username}/infoDevices/${mac}/${canId}/configModule`;
    const docRef = db.collection(path).doc(uid);

    const unsubscribe = docRef.onSnapshot((docSnapshot) => {
      if (docSnapshot.exists) {
        const data = docSnapshot.data();
        const item = data.item;
        console.log("Datos de voltaje actualizados:", data);

        if (item &&item.totalVoltage !== undefined) {
          setTotalVoltage(item.totalVoltage);
          console.log("Voltaje total actualizado:", item.totalVoltage);
        }
      } else {
        console.log("No se encontró el documento de tensión");
        setTotalVoltage(0);
      }
    }, (error) => {
      console.error("Error al escuchar cambios en voltaje:", error);
    });

    return () => {
      unsubscribe();
    };
  }, [usuario.username, uid]);

  // Calcular potencia cuando cambien corriente o voltaje
  useEffect(() => {
    const powerValue = totalCurrent * totalVoltage;
    setTotalPower(powerValue);
    console.log("Potencia total calculada:", powerValue);
  }, [totalCurrent, totalVoltage]);
  

  return (
    <Grid
      container
      direction="row"
      justifyContent="center"
      alignItems="center"
      style={style}
    >
      <HeadTile name={name} uid={uid} col={col} />

      {/* Valor de Amperaje */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          AMP
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalCurrent, 2)}
          </span>
        </h5>
      </Grid>

      {/* Valor de Voltaje */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          VOLT
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalVoltage, 1)}
          </span>
        </h5>
      </Grid>

      {/* Divider */}
      <Grid item xs={11}>
        <Divider className={classes.divider} />
      </Grid>

      {/* Valor de Potencia */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          WATT
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalPower, 2)}
          </span>
        </h5>
      </Grid>

      {/* Botón para mostrar/ocultar detalles */}
      <Grid item xs={12} container justifyContent="center">
        <IconButton
          className={classes.expandButton}
          onClick={() => setShowDetails(!showDetails)}
          size="small"
        >
          {showDetails ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Grid>

      {/* Sección de detalles expandible */}
      <Collapse in={showDetails} style={{ width: '100%' }}>
        <Grid item xs={12} style={{ padding: '0 16px' }}>
          <Box className={classes.detailsContainer}>
            {processCurrentsData().map((item, index) => (
              <div key={index} className={classes.currentItem}>
                <Typography className={classes.currentName}>
                  {item.name}
                </Typography>
                <Typography className={classes.currentValue}>
                  {formatValue(item.current, 2)} A
                </Typography>
              </div>
            ))}
            {processCurrentsData().length === 0 && (
              <Typography
                variant="body2"
                style={{ textAlign: 'center', color: '#666', padding: '16px' }}
              >
                No hay datos disponibles
              </Typography>
            )}
          </Box>
        </Grid>
      </Collapse>
    </Grid>
  )
}
